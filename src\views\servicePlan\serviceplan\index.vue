<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="老人姓名" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人姓名"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="elder in elderList"
            :key="elder.id"
            :label="elder.name"
            :value="elder.id"
          >
            <span>{{ elder.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入计划名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['servicePlan:service-plan:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['servicePlan:service-plan:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      :show-overflow-tooltip="true"
    >
      <el-table-column label="老人姓名" align="center" prop="elderName" min-width="100px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:service-plan:update']"
            class="elder-name-link"
          >
            {{ scope.row.elderName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="计划名称" align="center" prop="planName" min-width="120px"/>
      <el-table-column label="计划分类" align="center" prop="categoryName" min-width="120px"/>
      <el-table-column label="状态" align="center" prop="status" width="100px" :show-overflow-tooltip="false">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SERVICE_PLAN_STATUS" :value="String(scope.row.status)" />
        </template>
      </el-table-column>
      <el-table-column label="计划开始日期" align="center" prop="startDate" min-width="120px"/>
      <el-table-column label="计划结束日期" align="center" prop="endDate" min-width="120px"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column 
        label="操作" 
        align="center" 
        width="230px"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleCopy(scope.row)"
            v-hasPermi="['servicePlan:service-plan:create']"
          >
            复制
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['servicePlan:service-plan:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success" 
            @click="handleCreateTask(scope.row.id)"
          >
            制定任务
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['servicePlan:service-plan:delete']"
          >
            删除
          </el-button>
          
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ServicePlanForm ref="formRef" @success="getList" />

  <!-- 添加复制计划弹框 -->
  <copy-plan-dialog
    ref="copyDialogRef"
    :elder-list="elderList"
    @success="getList"
  />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ServicePlanApi, ServicePlanVO } from '@/api/servicePlan/serviceplan'
import ServicePlanForm from './ServicePlanForm.vue'
import { useRouter } from 'vue-router'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import CopyPlanDialog from './components/CopyPlanDialog.vue'
import { useDictStoreWithOut } from '@/store/modules/dict'

/** 服务计划 列表 */
defineOptions({ name: 'ServicePlan' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const router = useRouter()

const loading = ref(true) // 列表的加载中
const list = ref<ServicePlanVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  planName: undefined,
  status: undefined,
  startDate: [],
  endDate: [],
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 老人列表
const elderList = ref()

// 获取老人列表
const getElderList = async () => {
  try {
    const data = await ArchivesProfileApi.getArchivesProfileSimpleList()
    elderList.value = data
  
    
  } catch (error) {
    console.error('获取老人列表失败:', error)
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 先尝试正常的分页参数
    const params = {
      ...queryParams,
      pageNo: queryParams.pageNo || 1,
      pageSize: queryParams.pageSize || 10
    }
    console.log('查询参数:', params)

    const data = await ServicePlanApi.getServicePlanPage(params)
    console.log('API返回数据:', data)
    console.log('返回的列表长度:', data.list?.length)

    // 如果后端没有正确分页，在前端进行分页处理
    if (data.list && data.list.length > params.pageSize) {
      console.log('后端分页失效，使用前端分页')
      const startIndex = (params.pageNo - 1) * params.pageSize
      const endIndex = startIndex + params.pageSize
      const paginatedList = data.list.slice(startIndex, endIndex)

      list.value = paginatedList
      total.value = data.list.length // 使用实际返回的总数

      console.log('前端分页后的列表长度:', paginatedList.length)
      console.log('前端分页总数:', data.list.length)
    } else {
      // 后端分页正常工作
      list.value = data.list || []
      total.value = data.total || 0
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, elderList.value)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ServicePlanApi.deleteServicePlan(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ServicePlanApi.exportServicePlan(queryParams)
    download.excel(data, '服务计划.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 添加处理制定任务的方法 */
const handleCreateTask = (id: number) => {
  // 跳转到创建任务页面
  router.push({
    path: '/servicePlan/task-create',
    query: {
      planId: id
    }
  })
}

// 复制计划弹框 ref
const copyDialogRef = ref()

/** 处理复制操作 */
const handleCopy = (row: ServicePlanVO) => {
  copyDialogRef.value?.open(row)
}

const dictStore = useDictStoreWithOut()

/** 初始化 **/
onMounted(async () => {
  // 确保字典数据已加载
  await dictStore.setDictMap()
  await getElderList() // 先获取老人列表
  await getList()
})
</script>
